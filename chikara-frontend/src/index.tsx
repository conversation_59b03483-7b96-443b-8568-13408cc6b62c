import React, { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import "./app.css";
import AppRouter from "@/app/Routes/AppRouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import ErrorBoundary from "./components/Layout/ErrorBoundary";
import { ServiceWorkerUpdater } from "./components/ServiceWorker/ServiceWorkerUpdater";
import { initializeAnalytics } from "./lib/analytics";
import { createQueryClient } from "./lib/queryClient";

window.addEventListener("vite:preloadError", () => {
    window.location.reload();
});

// Initialize analytics services (Posthog and Sentry)
initializeAnalytics();

// Create the query client
const queryClient = createQueryClient();

ReactDOM.createRoot(document.getElementById("root")!).render(
    <StrictMode>
        <QueryClientProvider client={queryClient}>
            <ErrorBoundary>
                {import.meta.env.MODE !== "development" && <ServiceWorkerUpdater />}
                <AppRouter />
            </ErrorBoundary>
            <div className="hidden md:block">
                <ReactQueryDevtools initialIsOpen buttonPosition="top-left" position="right" />
            </div>
        </QueryClientProvider>
    </StrictMode>
);
