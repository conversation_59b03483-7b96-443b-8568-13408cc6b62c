import { cleanup } from "@testing-library/react";
import { afterEach, beforeEach, vi } from "vitest";
import "@testing-library/jest-dom/vitest";

// Global mocks for environment variables
vi.mock("import.meta", () => ({
    env: {
        VITE_IMAGE_CDN_URL: "https://cloudflare-image.jamessut.workers.dev",
        VITE_API_URL: "http://localhost:3000",
        NODE_ENV: "test",
    },
}));

// Mock Date for consistent testing
const mockDate = vi.fn();
const originalDate = Date;

beforeEach(() => {
    // Reset Date mock before each test
    vi.setSystemTime(new Date("2024-01-15T12:00:00Z"));
});

afterEach(() => {
    // Clean up after each test
    cleanup();
    vi.useRealTimers();
});

// Global DOM mocks
Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
    })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}));

// Mock console methods to reduce noise in tests
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

beforeEach(() => {
    console.warn = vi.fn();
    console.error = vi.fn();
});

afterEach(() => {
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
});

// Mock localStorage
const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
};
Object.defineProperty(window, "localStorage", {
    value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
};
Object.defineProperty(window, "sessionStorage", {
    value: sessionStorageMock,
});

// Mock fetch
global.fetch = vi.fn();

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn();
global.URL.revokeObjectURL = vi.fn();
